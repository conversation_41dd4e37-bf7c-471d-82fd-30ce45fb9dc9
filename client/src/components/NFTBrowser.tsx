import React, { useState, useEffect } from 'react';
import { useWeb3 } from '@/contexts/Web3Context';
import { useToast } from '@/hooks/use-toast';
import { ethers } from 'ethers';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

// ERC-721 interface for NFTs
const ERC721_ABI = [
  'function balanceOf(address owner) view returns (uint256)',
  'function tokenOfOwnerByIndex(address owner, uint256 index) view returns (uint256)',
  'function tokenURI(uint256 tokenId) view returns (string)',
  'function name() view returns (string)',
  'function symbol() view returns (string)'
];

// Interface for NFT item
interface NFTItem {
  contractAddress: string;
  tokenId: string;
  name: string;
  symbol: string;
  image?: string;
  attributes?: any[];
  metadata?: any;
}

interface NFTBrowserProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectNFT: (nft: NFTItem) => void;
  supportedCollections?: string[]; // Optional list of supported collection addresses
}

const NFTBrowser: React.FC<NFTBrowserProps> = ({
  isOpen,
  onClose,
  onSelectNFT,
  supportedCollections = []
}) => {
  const { provider, account, isConnected, connectWallet } = useWeb3();
  const { toast } = useToast();
  const [nfts, setNfts] = useState<NFTItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedNFT, setSelectedNFT] = useState<NFTItem | null>(null);

  // Define a function to get all NFT contracts owned by the user
  const getAllUserNFTContracts = async (userAddress: string, provider: any) => {
    try {
      // Use the Polygon API to get all NFT transactions for this wallet
      const apiUrl = `https://api.polygonscan.com/api?module=account&action=tokennfttx&address=${userAddress}&startblock=0&endblock=*********&sort=asc`;

      console.log(`Fetching all NFT transactions for wallet ${userAddress} using Polygon API`);
      const response = await fetch(apiUrl);
      const data = await response.json();

      if (data.status === '1' && data.result && Array.isArray(data.result)) {
        console.log(`Found ${data.result.length} NFT transactions`);

        // Extract unique contract addresses
        const contractAddresses = new Set<string>();

        for (const tx of data.result) {
          contractAddresses.add(tx.contractAddress);
        }

        console.log(`Found ${contractAddresses.size} unique NFT contracts`);
        return Array.from(contractAddresses);
      } else {
        console.error('Error fetching NFT transactions from Polygon API:', data.message || 'Unknown error');
        return [];
      }
    } catch (error) {
      console.error('Error calling Polygon API:', error);
      return [];
    }
  };

  // Fetch NFTs owned by the connected wallet
  const fetchNFTs = async () => {
    if (!provider || !account) {
      toast({
        title: 'Wallet Not Connected',
        description: 'Please connect your wallet to browse your NFTs',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    setNfts([]);

    try {
      // Check network first to avoid network errors
      const network = await provider.getNetwork();
      console.log('Current network when fetching NFTs:', {
        chainId: network.chainId,
        name: network.name
      });

      // Check if we're on a supported network (Polygon Mainnet, Mumbai, or Amoy)
      const supportedChainIds = [137, 80001, 80002]; // Polygon Mainnet, Mumbai, Amoy
      if (!supportedChainIds.includes(network.chainId)) {
        toast({
          title: 'Network Error',
          description: 'Please connect to Polygon network to browse your NFTs',
          variant: 'destructive'
        });
        setIsLoading(false);
        return;
      }

      const fetchedNFTs: NFTItem[] = [];

      // Get all NFT contract addresses for this user
      const contractAddresses = await getAllUserNFTContracts(account, provider);

      if (contractAddresses.length === 0) {
        console.log('No NFT contracts found for this wallet');
        setNfts([]);
        setIsLoading(false);
        toast({
          title: 'No NFTs Found',
          description: 'We couldn\'t find any NFTs in your wallet on this network.',
          variant: 'destructive'
        });
        return;
      }

      console.log(`Found ${contractAddresses.length} NFT contracts for this wallet`);

      // Check each contract for NFTs owned by the user
      for (const contractAddress of contractAddresses) {
        try {
          console.log(`Checking collection ${contractAddress}`);
          const nftContract = new ethers.Contract(
            contractAddress,
            ERC721_ABI,
            provider
          );

          // Get balance (number of NFTs owned in this collection)
          let balanceNumber = 0;
          try {
            const balance = await nftContract.balanceOf(account);
            balanceNumber = balance.toNumber();
            console.log(`User has ${balanceNumber} NFTs in collection ${contractAddress}`);
          } catch (balanceError: any) {
            console.error(`Error getting balance for collection ${contractAddress}:`, balanceError);
            // Continue to next collection
            continue;
          }

          if (balanceNumber > 0) {
            // Get collection name and symbol
            let name = 'Unknown Collection';
            let symbol = 'NFT';
            try {
              name = await nftContract.name();
              symbol = await nftContract.symbol();
            } catch (nameError) {
              console.error(`Error getting name/symbol for collection ${contractAddress}:`, nameError);
              // Continue with default values
            }

            // Fetch each token
            for (let i = 0; i < balanceNumber; i++) {
              try {
                // Get token ID
                const tokenId = await nftContract.tokenOfOwnerByIndex(account, i);
                console.log(`Found token ID ${tokenId} in collection ${contractAddress}`);

                // Get token metadata URI
                let tokenURI = '';
                try {
                  tokenURI = await nftContract.tokenURI(tokenId);
                } catch (uriError) {
                  console.error(`Error getting tokenURI for token ${tokenId}:`, uriError);
                  // Continue with empty URI
                }

                // Fetch metadata
                let metadata: Record<string, any> = {};
                let image = '';
                let attributes: any[] = [];

                if (tokenURI) {
                  try {
                    // Handle IPFS URIs
                    let uri = tokenURI;
                    if (uri.startsWith('ipfs://')) {
                      uri = uri.replace('ipfs://', 'https://ipfs.io/ipfs/');
                    }

                    const response = await fetch(uri);
                    metadata = await response.json();

                    // Extract image URL
                    if (metadata.image) {
                      image = metadata.image;
                      // Handle IPFS image URLs
                      if (image.startsWith('ipfs://')) {
                        image = image.replace('ipfs://', 'https://ipfs.io/ipfs/');
                      }
                    }

                    // Extract attributes if available
                    if (metadata.attributes) {
                      attributes = metadata.attributes;
                    }
                  } catch (metadataError) {
                    console.error(`Error fetching metadata for token ${tokenId}:`, metadataError);
                    // Continue with default values
                  }
                }

                // Add to NFTs array
                fetchedNFTs.push({
                  contractAddress,
                  tokenId: tokenId.toString(),
                  name,
                  symbol,
                  image,
                  attributes,
                  metadata
                });
              } catch (tokenError) {
                console.error(`Error fetching token ${i}:`, tokenError);
                // Continue to next token
              }
            }
          }
        } catch (contractError: any) {
          console.error(`Error checking collection ${contractAddress}:`, contractError);
          // Continue to next collection
        }
      }

      setNfts(fetchedNFTs);

      if (fetchedNFTs.length === 0) {
        toast({
          title: 'No NFTs Found',
          description: 'We couldn\'t find any NFTs in your wallet on this network.',
          variant: 'destructive'
        });
      } else {
        console.log(`Successfully loaded ${fetchedNFTs.length} NFTs from your wallet`);
      }
    } catch (error: any) {
      console.error('Error fetching NFTs:', error);

      // Check for network-related errors
      const errorMessage = error.message || 'Failed to fetch your NFTs. Please try again.';
      const isNetworkError = errorMessage.includes('network') || errorMessage.includes('chain');

      toast({
        title: isNetworkError ? 'Network Error' : 'Error',
        description: errorMessage,
        variant: 'destructive'
      });

      setNfts([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Connect wallet and fetch NFTs
  const handleConnectAndFetch = async () => {
    if (!isConnected) {
      await connectWallet();
    }
    fetchNFTs();
  };

  // When the dialog opens and wallet is connected, fetch NFTs
  useEffect(() => {
    let mounted = true;

    if (isOpen && isConnected) {
      // Use a timeout to ensure we don't block the UI
      const timer = setTimeout(() => {
        if (mounted) {
          fetchNFTs();
        }
      }, 100);

      return () => {
        mounted = false;
        clearTimeout(timer);
      };
    }

    return () => {
      mounted = false;
    };
  }, [isOpen, isConnected]);

  // Handle NFT selection
  const handleSelectNFT = (nft: NFTItem) => {
    setSelectedNFT(nft);
  };

  // Confirm selection
  const handleConfirmSelection = () => {
    if (selectedNFT) {
      onSelectNFT(selectedNFT);
      onClose();
    }
  };

  // Use a custom handler for dialog changes to ensure we don't interfere with game state
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // Only call onClose when the dialog is being closed
      onClose();
    }
    // We don't do anything when the dialog is being opened
    // This is handled by the NFTBrowserDialogProvider
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange} modal={false}>
      <DialogContent
        className="sm:max-w-[600px]"
        onPointerDownOutside={(e) => e.preventDefault()}
        onInteractOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>Select an NFT to Generate Your Pet Specter</DialogTitle>
          <DialogDescription>
            Choose one of your NFTs to use as a seed for generating a unique Pet Specter.
            Different NFTs will produce different types of specters with unique traits.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {!isConnected ? (
            <div className="flex justify-center">
              <Button onClick={handleConnectAndFetch}>
                Connect Wallet to Browse NFTs
              </Button>
            </div>
          ) : isLoading ? (
            <div className="flex flex-col items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin mb-2" />
              <p>Loading your NFTs...</p>
            </div>
          ) : nfts.length === 0 ? (
            <div className="text-center py-8">
              <p className="mb-4">No supported NFTs found in your wallet.</p>
              <Button onClick={fetchNFTs}>Refresh</Button>
            </div>
          ) : (
            <div>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 max-h-[400px] overflow-y-auto p-2">
                {nfts.map((nft, index) => (
                  <div
                    key={`${nft.contractAddress}-${nft.tokenId}-${index}`}
                    className={`border rounded-lg p-2 cursor-pointer transition-all ${
                      selectedNFT && selectedNFT.contractAddress === nft.contractAddress &&
                      selectedNFT.tokenId === nft.tokenId ?
                      'border-blue-500 bg-blue-50 dark:bg-blue-900/20' :
                      'hover:border-gray-400'
                    }`}
                    onClick={() => handleSelectNFT(nft)}
                  >
                    <div className="aspect-square w-full overflow-hidden rounded-md mb-2">
                      {nft.image ? (
                        <img
                          src={nft.image}
                          alt={`${nft.name} #${nft.tokenId}`}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = '/assets/textures/nft-placeholder.png';
                          }}
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                          <span>No Image</span>
                        </div>
                      )}
                    </div>
                    <div className="text-sm font-medium truncate">{nft.name}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">#{nft.tokenId}</div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmSelection}
            disabled={!selectedNFT || isLoading}
          >
            {selectedNFT ? 'Use Selected NFT' : 'Select an NFT'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default NFTBrowser;
