import { SpecterType } from '@/game/types';
import { toUtf8Bytes } from 'ethers/utils';
import { keccak256 } from 'ethers/crypto';

// Interface for NFT item from NFTBrowser
interface NFTItem {
  contractAddress: string;
  tokenId: string;
  name: string;
  symbol: string;
  image?: string;
  attributes?: any[];
  metadata?: any;
}

// Interface for generated pet data
interface GeneratedPetData {
  type: SpecterType;
  name: string;
  traits: {
    type: string;
    level: number;
    xp: number;
    xpToNextLevel: number;
  }[];
  stats: {
    health: number;
    maxHealth: number;
    attackPower: number;
    defenseValue: number;
    speed: number;
  };
}

/**
 * Service for generating pet specters based on NFT data
 */
export class PetGeneratorService {
  // Available specter types
  private static specterTypes: SpecterType[] = [
    { id: 0, name: 'WISP', color: '#3399ff', points: 100, texture: 'assets/textures/wisp.png' },
    { id: 1, name: '<PERSON><PERSON><PERSON><PERSON>', color: '#ff3399', points: 250, texture: 'assets/textures/phantom.png' },
    { id: 2, name: 'WRAIT<PERSON>', color: '#33ff99', points: 500, texture: 'assets/textures/wraith.png' },
    { id: 3, name: 'POLTERGEIST', color: '#9933ff', points: 1000, texture: 'assets/textures/poltergeist.png' },
    { id: 4, name: 'BANSHEE', color: '#ff9933', points: 2000, texture: 'assets/textures/banshee.png' }
  ];

  /**
   * Generate a pet specter based on NFT data
   */
  static generatePetFromNFT(nft: NFTItem): GeneratedPetData {
    // Create a deterministic hash from the NFT data
    const dataToHash = `${nft.contractAddress}-${nft.tokenId}-${nft.name}`;
    const hash = keccak256(toUtf8Bytes(dataToHash));
    
    // Use the hash to generate deterministic but seemingly random values
    const hashNumber = BigInt(hash);
    
    // Determine specter type based on hash
    const typeIndex = Number(hashNumber % BigInt(this.specterTypes.length));
    const specterType = this.specterTypes[typeIndex];
    
    // Generate a name based on the NFT
    const petName = this.generatePetName(nft);
    
    // Generate traits based on NFT attributes if available
    const traits = this.generateTraits(nft, hash);
    
    // Generate stats based on NFT and traits
    const stats = this.generateStats(nft, traits, hash);
    
    return {
      type: specterType,
      name: petName,
      traits,
      stats
    };
  }
  
  /**
   * Generate a pet name based on NFT data
   */
  private static generatePetName(nft: NFTItem): string {
    // Start with a default name based on the NFT
    let baseName = `${nft.name} Specter`;
    
    // If the NFT has a name attribute, use that
    if (nft.metadata && nft.metadata.name) {
      baseName = nft.metadata.name;
    }
    
    // Limit name length
    if (baseName.length > 20) {
      baseName = baseName.substring(0, 20);
    }
    
    return baseName;
  }
  
  /**
   * Generate traits based on NFT attributes
   */
  private static generateTraits(nft: NFTItem, hash: string): any[] {
    const traits = [
      { type: 'ATTACK', level: 1, xp: 0, xpToNextLevel: 100 },
      { type: 'DEFENSE', level: 1, xp: 0, xpToNextLevel: 100 },
      { type: 'SPEED', level: 1, xp: 0, xpToNextLevel: 100 },
      { type: 'INTELLIGENCE', level: 1, xp: 0, xpToNextLevel: 100 },
      { type: 'LOYALTY', level: 1, xp: 0, xpToNextLevel: 100 }
    ];
    
    // If NFT has attributes, use them to influence traits
    if (nft.attributes && nft.attributes.length > 0) {
      // Map common NFT attributes to our traits
      const attributeMap: Record<string, string> = {
        // Common attribute names mapped to our trait types
        'strength': 'ATTACK',
        'attack': 'ATTACK',
        'power': 'ATTACK',
        'defense': 'DEFENSE',
        'armor': 'DEFENSE',
        'protection': 'DEFENSE',
        'speed': 'SPEED',
        'agility': 'SPEED',
        'dexterity': 'SPEED',
        'intelligence': 'INTELLIGENCE',
        'wisdom': 'INTELLIGENCE',
        'smart': 'INTELLIGENCE',
        'loyalty': 'LOYALTY',
        'charisma': 'LOYALTY',
        'friendship': 'LOYALTY'
      };
      
      // Process each attribute
      nft.attributes.forEach(attr => {
        // Check if we have a trait name and value
        if (attr.trait_type && (attr.value !== undefined)) {
          // Convert trait type to lowercase for case-insensitive matching
          const traitTypeLower = attr.trait_type.toLowerCase();
          
          // Check if this attribute maps to one of our traits
          if (attributeMap[traitTypeLower]) {
            const traitType = attributeMap[traitTypeLower];
            const traitIndex = traits.findIndex(t => t.type === traitType);
            
            if (traitIndex >= 0) {
              // Convert attribute value to a level between 1-5
              let level = 1;
              
              // If value is a number, scale it
              if (typeof attr.value === 'number') {
                // Scale to 1-5 range, assuming most values are 0-100
                level = Math.max(1, Math.min(5, Math.ceil(attr.value / 20)));
              } 
              // If value is a string that represents a number
              else if (typeof attr.value === 'string' && !isNaN(Number(attr.value))) {
                const numValue = Number(attr.value);
                level = Math.max(1, Math.min(5, Math.ceil(numValue / 20)));
              }
              // If value is a string like "rare", "epic", etc.
              else if (typeof attr.value === 'string') {
                const valueLower = attr.value.toLowerCase();
                if (valueLower.includes('legendary') || valueLower.includes('mythic')) {
                  level = 5;
                } else if (valueLower.includes('epic')) {
                  level = 4;
                } else if (valueLower.includes('rare')) {
                  level = 3;
                } else if (valueLower.includes('uncommon')) {
                  level = 2;
                } else {
                  level = 1;
                }
              }
              
              // Update the trait
              traits[traitIndex].level = level;
            }
          }
        }
      });
    }
    
    // Use hash to add some randomness to traits that weren't set by attributes
    const hashBigInt = BigInt(hash);
    traits.forEach((trait, index) => {
      // Only modify traits that are still at level 1
      if (trait.level === 1) {
        // Use a different part of the hash for each trait
        const traitValue = Number((hashBigInt >> BigInt(index * 8)) & BigInt(0xFF));
        // Scale to 1-3 range for traits not explicitly set by NFT attributes
        trait.level = 1 + (traitValue % 3);
      }
    });
    
    return traits;
  }
  
  /**
   * Generate stats based on traits
   */
  private static generateStats(nft: NFTItem, traits: any[], hash: string): any {
    // Find trait levels
    const attackLevel = traits.find(t => t.type === 'ATTACK')?.level || 1;
    const defenseLevel = traits.find(t => t.type === 'DEFENSE')?.level || 1;
    const speedLevel = traits.find(t => t.type === 'SPEED')?.level || 1;
    const intelligenceLevel = traits.find(t => t.type === 'INTELLIGENCE')?.level || 1;
    
    // Calculate base stats from traits
    const baseHealth = 80 + (defenseLevel * 10);
    const baseAttack = 5 + (attackLevel * 3);
    const baseDefense = 3 + (defenseLevel * 2);
    const baseSpeed = 2 + (speedLevel * 0.5);
    
    // Add some randomness based on hash
    const hashBigInt = BigInt(hash);
    const healthMod = Number((hashBigInt >> BigInt(0)) & BigInt(0xFF)) % 20;
    const attackMod = Number((hashBigInt >> BigInt(8)) & BigInt(0xFF)) % 5;
    const defenseMod = Number((hashBigInt >> BigInt(16)) & BigInt(0xFF)) % 3;
    const speedMod = Number((hashBigInt >> BigInt(24)) & BigInt(0xFF)) % 2;
    
    // Final stats
    return {
      health: baseHealth + healthMod,
      maxHealth: baseHealth + healthMod,
      attackPower: baseAttack + attackMod,
      defenseValue: baseDefense + defenseMod,
      speed: baseSpeed + speedMod
    };
  }
  
  /**
   * Get the price for a pet based on its traits
   */
  static getPriceForGeneratedPet(pet: GeneratedPetData): string {
    // Calculate average trait level
    const totalLevel = pet.traits.reduce((sum, trait) => sum + trait.level, 0);
    const avgLevel = totalLevel / pet.traits.length;
    
    // Base price based on specter type
    let basePrice = 0;
    switch (pet.type.name) {
      case 'WISP': basePrice = 0.001; break;
      case 'PHANTOM': basePrice = 0.005; break;
      case 'WRAITH': basePrice = 0.01; break;
      case 'POLTERGEIST': basePrice = 0.02; break;
      case 'BANSHEE': basePrice = 0.05; break;
      default: basePrice = 0.001;
    }
    
    // Adjust price based on average trait level
    // Higher trait levels = higher price
    const levelMultiplier = 1 + ((avgLevel - 1) * 0.2); // 1.0 to 1.8x multiplier
    
    // Calculate final price
    const finalPrice = basePrice * levelMultiplier;
    
    // Return as string with 6 decimal places
    return finalPrice.toFixed(6);
  }
}
